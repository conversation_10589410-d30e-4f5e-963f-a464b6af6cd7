# Linux_Plan_Min 主配置文件
# 根据Plan_Min.md规范的动态任务图训练配置

# 基本配置
seed: 42
task: cereals

# 数据路径配置
data_root: /data2/syd_data/Breakfast_Data
output_dir: ${env:OUTPUT_DIR,/data2/syd_data/Breakfast_Data/Outputs}
stats_dir: ./stats

# 模型参数
model:
  # 特征维度 - 根据实际数据调整为64维（不包括时间戳）
  D: 64
  M: 48        # cereals任务完整的48个动作类别
  H: 256       # 隐藏层维度
  alpha: 0.05  # 动态更新的权重调整因子（根据Plan_Min.md）
  lr: 1e-4     # 学习率（根据Plan_Min.md）
  diff_enabled: true  # 启用动态差分更新

# 训练参数
trainer:
  max_epochs: 50
  batch_size: 16      # 适中的batch size
  num_workers: 4      # 数据加载worker数量
  accelerator: 'gpu'  # 使用GPU
  devices: 1          # 使用1块GPU
  precision: '16-mixed'  # 混合精度训练
  
  # 优化配置
  gradient_clip_val: 0.5
  accumulate_grad_batches: 1
  
  # 验证配置
  val_check_interval: 0.5  # 每半个epoch验证一次
  check_val_every_n_epoch: 1

# 数据配置
data:
  # 数据预处理参数
  normalize: true      # 启用归一化
  augmentation: false  # 时序数据不适合常规数据增强
  
  # 序列处理参数
  min_sequence_length: 5
  max_sequence_length: 2000

# 日志配置
logging:
  log_every_n_steps: 1    # 日志间隔
  save_top_k: 5
  monitor: val_segment_iou
  mode: max

# 实验配置
experiment:
  name: cereals_dynamic_graph
  version: null
  tags: ["cereals", "dynamic_graph", "breakfast", "plan_min"]

# 调试配置
debug:
  fast_dev_run: false
  overfit_batches: 0
  limit_train_batches: 1.0
  limit_val_batches: 1.0

# Hydra配置
hydra:
  run:
    dir: ${output_dir}/hydra_runs/${now:%Y-%m-%d_%H-%M-%S}
  job:
    chdir: true
