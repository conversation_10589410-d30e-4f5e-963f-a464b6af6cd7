"""
评估指标模块

提供段级序列评估的IoU和编辑距离指标
"""

from typing import List
import warnings

# 尝试导入Levenshtein库
try:
    import Levenshtein
    HAS_LEVENSHTEIN = True
except ImportError:
    HAS_LEVENSHTEIN = False
    warnings.warn("python-Levenshtein not installed. Using fallback implementation for edit distance.")


def greedy_merge(frame_seq: List[int]) -> List[int]:
    """
    将帧级序列合并为段级序列
    
    Args:
        frame_seq: 帧级动作序列
        
    Returns:
        段级动作序列（连续相同的动作被合并为一个段）
    """
    if not frame_seq: 
        return []
    
    segment_seq = [frame_seq[0]]
    for i in range(1, len(frame_seq)):
        if frame_seq[i] != frame_seq[i-1]:
            segment_seq.append(frame_seq[i])
    return segment_seq


def segment_level_iou(pred_seq: List[int], gt_seq: List[int]) -> float:
    """
    计算基于段级序列的IoU（交并比）
    
    该函数将段级序列重新对齐到相同的时间基准，然后计算IoU
    注意：此方法适用于段级序列（经过greedy_merge后的序列）
    
    Args:
        pred_seq: 预测的段级序列
        gt_seq: 真实的段级序列
        
    Returns:
        IoU分数，范围[0, 1]
    """
    if not pred_seq or not gt_seq:
        return 0.0
    
    # 为了正确计算IoU，我们需要将两个段级序列对齐到相同的时间基准
    # 使用较长序列的长度作为时间基准
    max_len = max(len(pred_seq), len(gt_seq))
    
    # 将段级序列扩展为时间序列（均匀分布）
    pred_timeline = []
    gt_timeline = []
    
    # 预测序列扩展
    for i, action in enumerate(pred_seq):
        # 计算该段在时间轴上的位置
        segment_len = max_len // len(pred_seq)
        extra = 1 if i < (max_len % len(pred_seq)) else 0
        pred_timeline.extend([action] * (segment_len + extra))
    
    # 真实序列扩展
    for i, action in enumerate(gt_seq):
        # 计算该段在时间轴上的位置
        segment_len = max_len // len(gt_seq)
        extra = 1 if i < (max_len % len(gt_seq)) else 0
        gt_timeline.extend([action] * (segment_len + extra))
    
    # 确保两个时间线长度相同
    min_timeline_len = min(len(pred_timeline), len(gt_timeline))
    pred_timeline = pred_timeline[:min_timeline_len]
    gt_timeline = gt_timeline[:min_timeline_len]
    
    if not pred_timeline:
        return 0.0
    
    # 计算逐帧匹配
    matches = sum(1 for p, g in zip(pred_timeline, gt_timeline) if p == g)
    
    return matches / len(pred_timeline)


def frame_accuracy(pred_seq: List[int], gt_seq: List[int]) -> float:
    """
    计算帧级准确度
    
    Args:
        pred_seq: 预测的帧级序列
        gt_seq: 真实的帧级序列
        
    Returns:
        准确度，范围[0, 1]
    """
    if not pred_seq or not gt_seq:
        return 0.0
    
    # 确保两个序列长度相同
    min_len = min(len(pred_seq), len(gt_seq))
    pred_seq = pred_seq[:min_len]
    gt_seq = gt_seq[:min_len]
    
    correct = sum(1 for p, g in zip(pred_seq, gt_seq) if p == g)
    return correct / len(pred_seq) if pred_seq else 0.0


def action_set_coverage(pred_seq: List[int], gt_seq: List[int]) -> float:
    """
    计算动作集合覆盖率（原来的iou_edit，重命名以明确其含义）
    
    注意：此指标衡量的是出现了哪些动作类型，不关心顺序和次数
    仅作为辅助指标使用，不应作为主要评估标准
    
    Args:
        pred_seq: 预测的段级序列
        gt_seq: 真实的段级序列
        
    Returns:
        覆盖率分数，范围[0, 1]
    """
    pred_segments = set(pred_seq)
    gt_segments = set(gt_seq)
    
    intersection = len(pred_segments.intersection(gt_segments))
    union = len(pred_segments.union(gt_segments))
    
    return intersection / union if union > 0 else 0.0


def _levenshtein_fallback(s1: str, s2: str) -> int:
    """
    编辑距离的回退实现
    """
    if len(s1) < len(s2):
        return _levenshtein_fallback(s2, s1)

    if len(s2) == 0:
        return len(s1)

    previous_row = list(range(len(s2) + 1))
    for i, c1 in enumerate(s1):
        current_row = [i + 1]
        for j, c2 in enumerate(s2):
            insertions = previous_row[j + 1] + 1
            deletions = current_row[j] + 1
            substitutions = previous_row[j] + (c1 != c2)
            current_row.append(min(insertions, deletions, substitutions))
        previous_row = current_row
    
    return previous_row[-1]


def lev_norm(pred_seq: List[int], gt_seq: List[int]) -> float:
    """
    计算归一化的编辑距离（Levenshtein distance）

    直接对整数序列计算编辑距离，避免字符转换的复杂性

    Args:
        pred_seq: 预测的段级序列
        gt_seq: 真实的段级序列

    Returns:
        归一化编辑距离，范围[0, 1]
    """
    if not pred_seq and not gt_seq:
        return 0.0

    # 直接对整数列表计算编辑距离
    if HAS_LEVENSHTEIN:
        # python-Levenshtein可以直接处理序列
        lev_dist = Levenshtein.distance(pred_seq, gt_seq)
    else:
        # 使用回退实现，将整数转换为字符串避免Unicode问题
        str_pred = ",".join(map(str, pred_seq))
        str_gt = ",".join(map(str, gt_seq))
        lev_dist = _levenshtein_fallback(str_pred, str_gt)

    max_len = max(len(pred_seq), len(gt_seq))
    return lev_dist / max_len if max_len > 0 else 0.0


def compute_metrics(pred_sequences: List[List[int]], gt_sequences: List[List[int]]) -> dict:
    """
    批量计算评估指标
    
    Args:
        pred_sequences: 预测序列列表
        gt_sequences: 真实序列列表
        
    Returns:
        包含各种评估指标的字典
    """
    if len(pred_sequences) != len(gt_sequences):
        raise ValueError("Prediction and ground truth sequences must have the same length")
    
    segment_iou_scores = []
    frame_acc_scores = []
    action_coverage_scores = []
    lev_scores = []
    
    for pred_frames, gt_frames in zip(pred_sequences, gt_sequences):
        # 转换为段级序列
        pred_segments = greedy_merge(pred_frames)
        gt_segments = greedy_merge(gt_frames)
        
        # 计算各种指标
        segment_iou_scores.append(segment_level_iou(pred_segments, gt_segments))
        frame_acc_scores.append(frame_accuracy(pred_frames, gt_frames))
        action_coverage_scores.append(action_set_coverage(pred_segments, gt_segments))
        lev_scores.append(lev_norm(pred_segments, gt_segments))
    
    return {
        'segment_iou_mean': sum(segment_iou_scores) / len(segment_iou_scores) if segment_iou_scores else 0.0,
        'frame_accuracy_mean': sum(frame_acc_scores) / len(frame_acc_scores) if frame_acc_scores else 0.0,
        'action_coverage_mean': sum(action_coverage_scores) / len(action_coverage_scores) if action_coverage_scores else 0.0,
        'lev_mean': sum(lev_scores) / len(lev_scores) if lev_scores else 0.0,
        'segment_iou_scores': segment_iou_scores,
        'frame_accuracy_scores': frame_acc_scores,
        'action_coverage_scores': action_coverage_scores,
        'lev_scores': lev_scores
    }


# 为了向后兼容，保留旧函数名但添加弃用警告
def iou_edit(pred_seq: List[int], gt_seq: List[int]) -> float:
    """
    已弃用：此函数计算的是动作集合覆盖率，不是真正的IoU
    请使用 segment_level_iou() 计算真正的时序IoU
    或使用 action_set_coverage() 来明确计算动作集合覆盖率
    """
    warnings.warn(
        "iou_edit() is deprecated and misleading. Use segment_level_iou() for temporal IoU "
        "or action_set_coverage() for action set coverage.",
        DeprecationWarning,
        stacklevel=2
    )
    return action_set_coverage(pred_seq, gt_seq)
