import pytorch_lightning as pl
import torch
import numpy as np
from glob import glob
from torch.utils.data import Dataset, DataLoader
import os
import logging
# 移除未使用的类型导入

# 设置日志
logger = logging.getLogger(__name__)


def custom_collate_fn(batch):
    """
    自定义批处理函数，处理可变长度序列和无效样本

    Args:
        batch: 批次数据，包含(V_seq, y_seq)元组或None值

    Returns:
        tuple: (V_padded, y_padded, lengths) 或抛出ValueError让DataLoader重新采样

    Raises:
        ValueError: 当整个批次被过滤为空时，让DataLoader重新采样
    """
    # 过滤掉数据集中返回None的无效样本
    batch = [item for item in batch if item is not None]
    if not batch:
        raise ValueError("empty batch")  # 抛出异常让DataLoader重新采样
    
    V_seqs, y_seqs = zip(*batch)
    max_len = max(len(y) for y in y_seqs)
    D = V_seqs[0].shape[1]
    
    V_padded = torch.zeros(len(V_seqs), max_len, D, dtype=torch.float32)
    y_padded = torch.full((len(y_seqs), max_len), -100, dtype=torch.long)  # -100是CrossEntropyLoss的忽略索引
    lengths = torch.tensor([len(y) for y in y_seqs], dtype=torch.long)

    for i, (v, y) in enumerate(zip(V_seqs, y_seqs)):
        len_i = len(y)
        V_padded[i, :len_i, :] = v
        y_padded[i, :len_i] = y
    return V_padded, y_padded, lengths


class BreakfastDataset(Dataset):
    """
    完整Breakfast数据集类，支持所有10个动作大类
    支持从.npy和.txt特征文件和分段标签文件加载数据
    """
    def __init__(self, root: str, split: str, action_map: dict):
        self.root = root
        self.split = split
        self.action_map = action_map
        # 10个动作大类
        self.action_categories = [
            "cereals", "coffee", "friedegg", "juice", "milk", 
            "pancake", "salat", "sandwich", "scrambledegg", "tea"
        ]
        self.pairs = self._find_files()

    def _find_files(self):
        """查找所有动作类别的特征文件和对应的标签文件，优先使用npy格式"""
        pairs = []
        subjects = ("s1", "s2", "s3") if self.split == "train" else ("s4",)
        
        total_npy_files = 0
        total_txt_files = 0
        
        for subj in subjects:
            for category in self.action_categories:
                # 优先查找.npy格式的特征文件
                npy_feature_pattern = f"{self.root}/breakfast_data_npy/{subj}/{category}/*.npy"
                npy_feature_files = glob(npy_feature_pattern)
                
                if npy_feature_files:
                    # 如果存在npy格式，使用npy格式
                    for feature_file in npy_feature_files:
                        # 构建对应的标签文件路径
                        base_name = os.path.basename(feature_file)
                        
                        # 标签文件路径格式（npy格式）
                        label_file = os.path.join(
                            self.root,
                            "segmentation_coarse_npy",
                            f"{subj}_label",
                            category,
                            base_name
                        )
                        
                        if os.path.exists(label_file):
                            pairs.append((feature_file, label_file))
                            total_npy_files += 1
                else:
                    # 回退到txt格式
                    feature_pattern = f"{self.root}/breakfast_data/{subj}/{category}/*.txt"
                    feature_files = glob(feature_pattern)
                    
                    for feature_file in feature_files:
                        # 构建对应的标签文件路径
                        base_name = os.path.basename(feature_file)
                        
                        # 标签文件路径格式（txt格式）
                        label_file = os.path.join(
                            self.root,
                            "segmentation_coarse",
                            f"{subj}_label",
                            category,
                            base_name
                        )
                        
                        if os.path.exists(label_file):
                            pairs.append((feature_file, label_file))
                            total_txt_files += 1
        
        logger.info(f"数据加载统计 - {self.split}: npy格式 {total_npy_files} 个, txt格式 {total_txt_files} 个, 总计 {len(pairs)} 个文件")
        return pairs

    def _parse_segment_labels(self, label_file: str, num_frames: int):
        """
        解析分段标签文件并转换为逐帧标签
        
        Args:
            label_file: 标签文件路径
            num_frames: 特征序列的总帧数
            
        Returns:
            np.array: 逐帧标签数组
        """
        frame_labels = np.full(num_frames, -1, dtype=np.int32)  # 初始化为-1
        
        with open(label_file, 'r') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue
                    
                # 解析 "1-30 SIL" 格式
                parts = line.split()
                if len(parts) >= 2:
                    frame_range = parts[0]
                    action_name = parts[1]
                    
                    # 解析帧范围
                    if '-' in frame_range:
                        start_str, end_str = frame_range.split('-')
                        start_frame = int(start_str) - 1  # 转换为0-based索引
                        end_frame = int(end_str) - 1      # 转换为0-based索引
                        
                        # 映射动作名称到索引
                        if action_name in self.action_map:
                            action_idx = self.action_map[action_name]
                            # 确保索引在有效范围内
                            start_frame = max(0, min(start_frame, num_frames-1))
                            end_frame = max(0, min(end_frame, num_frames-1))
                            frame_labels[start_frame:end_frame+1] = action_idx
        
        return frame_labels

    def __len__(self):
        return len(self.pairs)

    def __getitem__(self, i):
        """加载单个样本，支持npy和txt格式"""
        try:
            feature_file, label_file = self.pairs[i]
            
            # 根据文件扩展名选择加载方式
            if feature_file.endswith('.npy'):
                # 加载npy格式特征文件
                V = torch.from_numpy(np.load(feature_file)).float()
                
                # 加载npy格式标签文件
                frame_labels = np.load(label_file)
            else:
                # 加载txt格式特征文件（跳过第一列时间戳）
                data = np.loadtxt(feature_file)
                if data.ndim == 1:
                    data = data.reshape(1, -1)
                
                # 提取特征（跳过第一列时间戳）
                V = torch.from_numpy(data[:, 1:]).float()  # 跳过第一列
                num_frames = len(V)
                
                # 解析分段标签文件
                frame_labels = self._parse_segment_labels(label_file, num_frames)
            
            # 过滤掉未映射的标签（-1）
            valid_indices = frame_labels != -1
            if not valid_indices.any():
                logger.warning(f"No valid labels found in {label_file}")
                return None
                
            V_valid = V[valid_indices]
            y_valid = torch.tensor(frame_labels[valid_indices], dtype=torch.long)

            # 如果过滤后序列太短，则返回None
            if len(y_valid) < 2:
                return None
                
            return (V_valid, y_valid)
            
        except Exception as e:
            logger.warning(f"Error loading {self.pairs[i]}: {e}")
            return None


class BreakfastDataModule(pl.LightningDataModule):
    """
    PyTorch Lightning数据模块，管理训练、验证和测试数据集
    支持完整的Breakfast数据集（所有10个动作大类）
    """
    def __init__(self, data_root: str, batch_size: int, action_map: dict, num_workers: int = 4):
        super().__init__()
        self.data_root = data_root
        self.batch_size = batch_size
        self.action_map = action_map
        self.num_workers = num_workers

    def setup(self, stage: str):
        """设置数据集"""
        if stage == 'fit' or stage is None:
            self.train_dataset = BreakfastDataset(self.data_root, "train", self.action_map)
            self.val_dataset = BreakfastDataset(self.data_root, "test", self.action_map)
        if stage == 'test' or stage is None:
            self.test_dataset = BreakfastDataset(self.data_root, "test", self.action_map)

    def train_dataloader(self):
        return DataLoader(
            self.train_dataset, 
            self.batch_size, 
            shuffle=True, 
            collate_fn=custom_collate_fn, 
            num_workers=self.num_workers
        )

    def val_dataloader(self):
        return DataLoader(
            self.val_dataset, 
            self.batch_size, 
            collate_fn=custom_collate_fn, 
            num_workers=self.num_workers
        )

    def test_dataloader(self):
        return DataLoader(
            self.test_dataset, 
            self.batch_size, 
            collate_fn=custom_collate_fn, 
            num_workers=self.num_workers
        )
