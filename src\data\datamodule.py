import pytorch_lightning as pl
import torch
import numpy as np
from glob import glob
from torch.utils.data import Dataset, DataLoader, TensorDataset
import os
import logging
# 移除未使用的类型导入

# 设置日志
logger = logging.getLogger(__name__)


def custom_collate_fn(batch):
    """
    自定义批处理函数，处理可变长度序列和无效样本

    Args:
        batch: 批次数据，包含(V_seq, y_seq)元组或None值

    Returns:
        tuple: (V_padded, y_padded, lengths) 或抛出ValueError让DataLoader重新采样

    Raises:
        ValueError: 当整个批次被过滤为空时，让DataLoader重新采样
    """
    # 过滤掉数据集中返回None的无效样本
    batch = [item for item in batch if item is not None]
    if not batch:
        raise ValueError("empty batch")  # 抛出异常让DataLoader重新采样
    
    V_seqs, y_seqs = zip(*batch)
    max_len = max(len(y) for y in y_seqs)
    D = V_seqs[0].shape[1]
    
    V_padded = torch.zeros(len(V_seqs), max_len, D, dtype=torch.float32)
    y_padded = torch.full((len(y_seqs), max_len), -100, dtype=torch.long)  # -100是CrossEntropyLoss的忽略索引
    lengths = torch.tensor([len(y) for y in y_seqs], dtype=torch.long)

    for i, (v, y) in enumerate(zip(V_seqs, y_seqs)):
        len_i = len(y)
        V_padded[i, :len_i, :] = v
        y_padded[i, :len_i] = y
    return V_padded, y_padded, lengths


class CerealsDataset(Dataset):
    """
    Cereals任务专用数据集类，根据Plan_Min.md规范实现
    只加载cereals任务的数据，支持从.npy特征文件和分段标签文件加载数据
    """
    def __init__(self, root: str, split: str, action_map: dict):
        self.root = root
        self.split = split
        self.action_map = action_map
        self.pairs = self._find_files()

    def _find_files(self):
        """根据Plan_Min.md规范查找cereals任务的特征文件和对应的标签文件"""
        pairs = []
        subjects = ("s1", "s2", "s3") if self.split == "train" else ("s4",)

        for subj in subjects:
            # 根据Plan_Min.md的目录结构规范
            for npy_file in glob(f"{self.root}/breakfast_data/{subj}/cereals/P??_camera*.npy"):
                # 构建对应的标签文件路径
                txt_file = npy_file.replace("breakfast_data", "segmentation_coarse").replace(".npy", ".txt").replace(subj, f"{subj}_label")
                if os.path.exists(txt_file):
                    pairs.append((npy_file, txt_file))

        logger.info(f"Cereals数据加载统计 - {self.split}: 找到 {len(pairs)} 个文件对")
        return pairs



    def __len__(self):
        return len(self.pairs)

    def __getitem__(self, i):
        """根据Plan_Min.md规范加载单个cereals样本"""
        try:
            npy_file, txt_file = self.pairs[i]

            # 加载npy格式特征文件
            V = torch.from_numpy(np.load(npy_file)).float()

            # 解析txt格式标签文件，转换为逐帧标签
            y_raw = np.loadtxt(txt_file, dtype=np.int32)[:, 1]  # 第二列是动作标签（1-based）
            y = torch.tensor([self.action_map.get(str(label), -1) for label in y_raw], dtype=torch.long)

            # 过滤掉未映射的标签（如背景）
            valid_indices = y != -1
            V_valid, y_valid = V[valid_indices], y[valid_indices]

            # 如果过滤后序列为空，则返回None，由collate_fn处理
            return (V_valid, y_valid) if len(y_valid) > 1 else None

        except Exception as e:
            logger.warning(f"Error loading {self.pairs[i]}: {e}")
            return None


class BreakfastDataModule(pl.LightningDataModule):
    """
    PyTorch Lightning数据模块，专门用于cereals任务
    根据Plan_Min.md规范实现
    """
    def __init__(self, data_root: str, batch_size: int, action_map: dict, num_workers: int = 4):
        super().__init__()
        self.data_root = data_root
        self.batch_size = batch_size
        self.action_map = action_map
        self.num_workers = num_workers

    def setup(self, stage: str):
        """设置数据集"""
        if stage == 'fit' or stage is None:
            self.train_dataset = CerealsDataset(self.data_root, "train", self.action_map)
            self.val_dataset = CerealsDataset(self.data_root, "test", self.action_map)
        if stage == 'test' or stage is None:
            self.test_dataset = CerealsDataset(self.data_root, "test", self.action_map)

    def train_dataloader(self):
        # 如果数据集为空，创建虚拟数据用于测试
        if len(self.train_dataset) == 0:
            logger.warning("Train dataset is empty, creating dummy data for testing")
            return self._create_dummy_dataloader()

        return DataLoader(
            self.train_dataset,
            self.batch_size,
            shuffle=True,
            collate_fn=custom_collate_fn,
            num_workers=self.num_workers
        )

    def val_dataloader(self):
        # 如果数据集为空，创建虚拟数据用于测试
        if len(self.val_dataset) == 0:
            logger.warning("Val dataset is empty, creating dummy data for testing")
            return self._create_dummy_dataloader()

        return DataLoader(
            self.val_dataset,
            self.batch_size,
            collate_fn=custom_collate_fn,
            num_workers=self.num_workers
        )

    def test_dataloader(self):
        # 如果数据集为空，创建虚拟数据用于测试
        if len(self.test_dataset) == 0:
            logger.warning("Test dataset is empty, creating dummy data for testing")
            return self._create_dummy_dataloader()

        return DataLoader(
            self.test_dataset,
            self.batch_size,
            collate_fn=custom_collate_fn,
            num_workers=self.num_workers
        )

    def _create_dummy_dataloader(self):
        """创建虚拟数据加载器用于测试"""
        from torch.utils.data import TensorDataset

        # 创建虚拟数据：2个样本，每个样本10帧，64维特征
        batch_size = min(self.batch_size, 2)
        seq_len = 10
        D = 64
        M = len(self.action_map)

        # 创建虚拟特征和标签
        V_dummy = torch.randn(batch_size, seq_len, D)
        y_dummy = torch.randint(0, M, (batch_size, seq_len))
        lengths_dummy = torch.full((batch_size,), seq_len)

        # 创建TensorDataset
        dummy_dataset = TensorDataset(V_dummy, y_dummy, lengths_dummy)

        return DataLoader(
            dummy_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=0  # 虚拟数据不需要多进程
        )
