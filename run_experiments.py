#!/usr/bin/env python3
"""
实验管理脚本
按顺序执行静态基线和动态差分更新方法的训练，并生成对比分析报告
"""

import os
import sys
import time
import subprocess
import json
from pathlib import Path
from datetime import datetime
import torch

def print_header(title):
    """打印格式化的标题"""
    print("\n" + "="*80)
    print(f"🚀 {title}")
    print("="*80)

def print_section(title):
    """打印章节标题"""
    print(f"\n📋 {title}")
    print("-"*50)

def check_environment():
    """检查运行环境"""
    print_section("环境检查")
    
    # 检查GPU
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"✅ GPU: {gpu_name} ({gpu_memory:.1f} GB)")
        print(f"✅ CUDA Version: {torch.version.cuda}")
        print(f"✅ PyTorch Version: {torch.__version__}")
    else:
        print("❌ GPU not available!")
        return False
    
    # 检查数据目录
    data_root = "/data2/syd_data/Breakfast_Data"
    if os.path.exists(data_root):
        print(f"✅ 数据目录存在: {data_root}")
    else:
        print(f"❌ 数据目录不存在: {data_root}")
        return False
    
    # 检查输出目录
    output_dir = "/data2/syd_data/Breakfast_Data/Outputs"
    os.makedirs(output_dir, exist_ok=True)
    print(f"✅ 输出目录: {output_dir}")
    
    return True

def run_training(script_name, experiment_name):
    """运行训练脚本"""
    print_section(f"开始 {experiment_name}")
    
    start_time = time.time()
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 运行训练脚本
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=False, 
                              text=True, 
                              check=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ {experiment_name} 训练完成!")
        print(f"⏱️ 耗时: {duration/60:.1f} 分钟")
        
        return True, duration
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {experiment_name} 训练失败!")
        print(f"错误: {e}")
        return False, 0
    except KeyboardInterrupt:
        print(f"⚠️ {experiment_name} 被用户中断")
        return False, 0

def generate_comparison_report():
    """生成对比分析报告"""
    print_section("生成对比分析报告")
    
    report_data = {
        "experiment_date": datetime.now().isoformat(),
        "gpu_info": {
            "name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else "N/A",
            "memory_gb": torch.cuda.get_device_properties(0).total_memory / 1024**3 if torch.cuda.is_available() else 0
        },
        "pytorch_version": torch.__version__,
        "cuda_version": torch.version.cuda,
    }
    
    # 保存实验信息
    output_dir = "/data2/syd_data/Breakfast_Data/Outputs"
    report_path = os.path.join(output_dir, "experiment_report.json")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"📊 实验报告已保存: {report_path}")
    
    # 检查训练结果
    static_dir = os.path.join(output_dir, "static_baseline")
    dynamic_dir = os.path.join(output_dir, "dynamic_method")
    
    print("\n📈 训练结果检查:")
    if os.path.exists(static_dir):
        print(f"✅ 静态基线结果: {static_dir}")
        if os.path.exists(os.path.join(static_dir, "final_static_model.ckpt")):
            print("   ✅ 静态模型已保存")
    else:
        print("❌ 静态基线结果不存在")
    
    if os.path.exists(dynamic_dir):
        print(f"✅ 动态方法结果: {dynamic_dir}")
        if os.path.exists(os.path.join(dynamic_dir, "final_dynamic_model.ckpt")):
            print("   ✅ 动态模型已保存")
    else:
        print("❌ 动态方法结果不存在")

def main():
    """主函数"""
    print_header("Plan_Min 实验执行 - GPU优化版本")
    
    # 记录开始时间
    total_start_time = time.time()
    
    # 1. 环境检查
    if not check_environment():
        print("❌ 环境检查失败，退出实验")
        return
    
    # 2. 执行训练实验
    experiments = [
        ("train_static.py", "静态基线方法"),
        ("train_dynamic.py", "动态差分更新方法")
    ]
    
    results = {}
    
    for script, name in experiments:
        if not os.path.exists(script):
            print(f"❌ 训练脚本不存在: {script}")
            continue
            
        success, duration = run_training(script, name)
        results[name] = {
            "success": success,
            "duration_minutes": duration / 60
        }
        
        if not success:
            print(f"⚠️ {name} 失败，但继续执行后续实验...")
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        print(f"💤 等待3秒后继续下一个实验...")
        time.sleep(3)
    
    # 3. 生成报告
    generate_comparison_report()
    
    # 4. 总结
    total_duration = time.time() - total_start_time
    print_header("实验总结")
    
    for name, result in results.items():
        status = "✅ 成功" if result["success"] else "❌ 失败"
        duration = result["duration_minutes"]
        print(f"{status} {name}: {duration:.1f} 分钟")
    
    print(f"\n⏱️ 总耗时: {total_duration/60:.1f} 分钟")
    print(f"📁 结果目录: /data2/syd_data/Breakfast_Data/Outputs")
    
    # 5. 下一步提示
    print_section("下一步操作建议")
    print("1. 检查训练日志和TensorBoard记录")
    print("2. 运行模型评估和对比分析")
    print("3. 生成学术报告和可视化图表")
    print("\n命令示例:")
    print("   tensorboard --logdir /data2/syd_data/Breakfast_Data/Outputs")
    print("   python src/utils/academic_report.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 实验被用户中断")
    except Exception as e:
        print(f"\n❌ 实验执行出错: {e}")
        raise 