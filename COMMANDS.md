# 🚀 Plan_Min 指令清单

## 核心训练指令

### 1. 完整实验运行（推荐）
```bash
python run_experiments.py
```
**作用**: 自动按顺序训练静态基线→动态方法，并生成对比报告  
**耗时**: ~60-90分钟（A6000 GPU）  
**输出**: `/data2/syd_data/Breakfast_Data/Outputs/`
- `static_baseline/` - 静态基线结果
- `dynamic_method/` - 动态方法结果  
- `experiment_report.json` - 实验总结

### 2. 静态基线训练
```bash
python train_static.py
```
**作用**: 训练不使用差分更新的基线模型  
**耗时**: ~30-45分钟  
**输出**: `/data2/syd_data/Breakfast_Data/Outputs/static_baseline/`
- `final_static_model.ckpt` - 最终模型权重
- `checkpoints/` - 训练检查点
- `static_baseline_logs/` - TensorBoard日志

### 3. 动态方法训练
```bash
python train_dynamic.py
```
**作用**: 训练带差分更新的动态模型，如有静态模型会自动加载初始化  
**耗时**: ~30-45分钟  
**输出**: `/data2/syd_data/Breakfast_Data/Outputs/dynamic_method/`
- `final_dynamic_model.ckpt` - 最终模型权重
- `checkpoints/` - 训练检查点  
- `dynamic_method_logs/` - TensorBoard日志

## 监控指令

### 4. GPU状态监控
```bash
watch -n 1 nvidia-smi
```
**作用**: 实时监控GPU使用率、显存占用、温度功耗  
**显示**: GPU利用率应保持在80%+以获得最佳性能

### 5. 训练进程检查
```bash
ps aux | grep python | grep train
```
**作用**: 查看当前运行的训练进程状态  
**输出**: 显示活跃的训练进程PID和命令

### 6. TensorBoard可视化
```bash
tensorboard --logdir /data2/syd_data/Breakfast_Data/Outputs
```
**作用**: 启动TensorBoard服务，可视化训练指标  
**访问**: 浏览器打开 http://localhost:6006  
**内容**: 训练/验证损失、IoU、F1-score等指标曲线

## 结果分析指令

### 7. 检查训练结果
```bash
ls -la /data2/syd_data/Breakfast_Data/Outputs/
```
**作用**: 查看所有实验输出文件和目录结构  
**显示**: 静态/动态方法的完整结果目录

### 8. 查看实验报告
```bash
cat /data2/syd_data/Breakfast_Data/Outputs/experiment_report.json
```
**作用**: 查看实验的基本配置信息  
**内容**: GPU信息、PyTorch版本、实验时间等

### 9. 训练日志查看
```bash
# 查看最新训练日志
find /data2/syd_data/Breakfast_Data/Outputs -name "*.csv" -exec tail -5 {} \;
```
**作用**: 快速查看训练的最新指标数据  
**内容**: epoch, loss, val_iou, val_f1等指标

## 环境检查指令

### 10. CUDA环境检查
```bash
python -c "import torch; print(f'CUDA: {torch.cuda.is_available()}, Version: {torch.version.cuda}')"
```
**作用**: 验证CUDA和PyTorch配置  
**输出**: CUDA可用性和版本信息

### 11. 数据路径检查
```bash
ls /data2/syd_data/Breakfast_Data/breakfast_data/s1/cereals/ | head -5
```
**作用**: 确认数据集是否正确挂载  
**输出**: 显示cereals任务的特征文件列表

## 快速排错指令

### 12. 清理GPU缓存
```bash
python -c "import torch; torch.cuda.empty_cache(); print('GPU cache cleared')"
```
**作用**: 释放GPU显存，解决内存不足问题

### 13. 杀死训练进程
```bash
pkill -f "train_"
```
**作用**: 强制终止所有训练进程（训练卡住时使用）

---

## 💡 使用建议

1. **首次使用**: 直接运行 `python run_experiments.py`
2. **调试实验**: 先运行 `python train_static.py` 测试环境
3. **监控训练**: 开两个终端，一个运行训练，一个运行 `watch -n 1 nvidia-smi`
4. **结果分析**: 训练完成后启动 TensorBoard 查看指标曲线

**注意**: 所有训练指令都会自动使用GPU 0，确保其他程序不占用该GPU以获得最佳性能。 