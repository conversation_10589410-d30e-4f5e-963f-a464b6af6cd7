import pytorch_lightning as pl
import torch
import torch.nn.functional as F


class TrainerModule(pl.LightningModule):
    """
    PyTorch Lightning训练模块

    整合差分MLP和任务图，实现完整的训练和验证流程
    """

    def __init__(self, diff_mlp, task_graph, learning_rate: float = 1e-4, diff_enabled: bool = True):
        """
        Args:
            diff_mlp: 差分更新MLP网络
            task_graph: 任务图模块
            learning_rate: 学习率
            diff_enabled: 是否启用差分更新，False时作为静态基线
        """
        super().__init__()

        self.diff_mlp = diff_mlp
        self.g = task_graph
        self.lr = learning_rate
        self.diff_enabled = diff_enabled

        # 为静态基线创建一个简单的可学习参数
        # 虽然静态基线主要使用W0，但为了让PyTorch Lightning正常工作，
        # 我们添加一个小的可学习偏置项
        if not diff_enabled:
            self.static_bias = torch.nn.Parameter(torch.zeros(task_graph.W0.shape[1]))
        
        # 存储验证步骤的输出
        self.validation_step_outputs = []

        # 保存超参数（忽略模型对象）
        self.save_hyperparameters(ignore=['diff_mlp', 'task_graph'])

    def training_step(self, batch, batch_idx):
        """训练步骤"""
        V_padded, y_padded, lengths = batch

        # 优雅处理整个批次无效的情况
        if V_padded is None:
            return None

        # 确保 total_loss 在正确设备上初始化
        total_loss = torch.tensor(0., device=self.device)
        num_steps = 0

        # 正确处理批处理数据
        for i in range(V_padded.shape[0]):  # 遍历批次中的每个序列
            # 修复GPU tensor切片索引问题：必须调用.item()转为Python int
            len_i = lengths[i].item()
            V_seq = V_padded[i, :len_i]
            y_seq = y_padded[i, :len_i]

            for k in range(len(V_seq) - 1):  # 遍历序列中的每一帧
                V_k, n_k = V_seq[k], y_seq[k].item()
                n_k_plus_1 = y_seq[k+1]

                # 根据diff_enabled决定是否使用差分更新
                if self.diff_enabled:
                    # 动态方法：计算差分特征
                    diff = torch.abs(V_k - self.g.proto[n_k])
                    # 通过MLP预测权重调整量
                    dW_k = self.diff_mlp(diff)
                    # 计算下一步的logits（非累积更新）
                    logits_k = self.g.get_next_step_logits(n_k, dW_k)
                else:
                    # 静态基线：直接使用W0 + 可学习偏置，完全不涉及差分更新
                    logits_k = self.g.W0[n_k] + self.static_bias

                # 计算交叉熵损失
                loss_k = F.cross_entropy(logits_k.unsqueeze(0), n_k_plus_1.unsqueeze(0))
                total_loss += loss_k
                num_steps += 1

        # 计算平均损失
        if num_steps > 0:
            avg_loss = total_loss / num_steps
        else:
            # 如果没有有效步骤，返回一个小的损失避免训练失败
            avg_loss = torch.tensor(0.0, device=self.device, requires_grad=True)

        # 记录训练损失
        self.log("train_loss", avg_loss, on_step=True, on_epoch=True, prog_bar=True, batch_size=V_padded.shape[0])
        return avg_loss

    def validation_step(self, batch, batch_idx):
        """验证步骤 - 修复了Teacher Forcing问题"""
        V_padded, y_padded, lengths = batch
        if V_padded is None:
            return None

        predictions, ground_truths = [], []

        for i in range(V_padded.shape[0]):
            self.g.reset()  # 每个序列开始前重置图状态
            # 修复GPU tensor切片索引问题：必须调用.item()转为Python int
            len_i = lengths[i].item()
            V_seq = V_padded[i, :len_i]
            y_seq = y_padded[i, :len_i]

            # 真正的序列预测：不使用Teacher Forcing
            # 只使用第一帧的真实标签作为起始点
            pred_frame_seq = [y_seq[0].item()]
            current_predicted_node = y_seq[0].item()

            for k in range(len(V_seq) - 1):
                V_k = V_seq[k]

                # 根据diff_enabled决定是否使用差分更新
                if self.diff_enabled:
                    # 动态方法：基于当前预测的节点计算差分特征
                    diff = torch.abs(V_k - self.g.proto[current_predicted_node])
                    dW_k = self.diff_mlp(diff)
                    # 更新累积图
                    self.g.step_and_update(current_predicted_node, dW_k)
                    # 从更新后的图中预测下一步
                    next_step_logits = self.g.W[current_predicted_node]
                else:
                    # 静态基线：直接使用W0 + 可学习偏置
                    next_step_logits = self.g.W0[current_predicted_node] + self.static_bias
                
                # 预测下一个节点
                predicted_next_node = torch.argmax(next_step_logits).item()
                pred_frame_seq.append(predicted_next_node)
                
                # 更新当前预测节点（关键修复：使用预测值而非真实值）
                current_predicted_node = predicted_next_node

            predictions.append(pred_frame_seq)
            ground_truths.append(y_seq.tolist())

        output = {"preds": predictions, "gts": ground_truths}
        self.validation_step_outputs.append(output)
        return output

    def on_validation_epoch_end(self):
        """验证轮次结束时的处理 - 使用修正的评估指标"""
        # 导入修正后的评估指标
        try:
            from ..utils.metrics import greedy_merge, segment_level_iou, frame_accuracy, action_set_coverage, lev_norm
        except ImportError:
            # 如果还没有实现metrics模块，暂时跳过评估
            self.validation_step_outputs.clear()
            return

        # 使用收集的验证步骤输出
        outputs = self.validation_step_outputs
        if not outputs:
            return

        all_preds, all_gts = [], []

        for out in outputs:
            if out and isinstance(out, dict):
                all_preds.extend(out.get("preds", []))
                all_gts.extend(out.get("gts", []))

        if not all_preds:
            self.validation_step_outputs.clear()
            return

        # 计算多种评估指标
        segment_iou_scores = []
        frame_acc_scores = []
        action_coverage_scores = []
        lev_scores = []
        
        for pred_frames, gt_frames in zip(all_preds, all_gts):
            pred_segments = greedy_merge(pred_frames)
            gt_segments = greedy_merge(gt_frames)
            
            # 使用修正后的评估指标
            segment_iou_scores.append(segment_level_iou(pred_segments, gt_segments))
            frame_acc_scores.append(frame_accuracy(pred_frames, gt_frames))
            action_coverage_scores.append(action_set_coverage(pred_segments, gt_segments))
            lev_scores.append(lev_norm(pred_segments, gt_segments))

        # 计算平均值
        avg_segment_iou = torch.tensor(segment_iou_scores).mean() if segment_iou_scores else 0.0
        avg_frame_acc = torch.tensor(frame_acc_scores).mean() if frame_acc_scores else 0.0
        avg_action_coverage = torch.tensor(action_coverage_scores).mean() if action_coverage_scores else 0.0
        avg_lev = torch.tensor(lev_scores).mean() if lev_scores else 0.0

        # 记录所有指标
        metrics_dict = {
            "val_segment_iou": avg_segment_iou,      # 真正的时序IoU
            "val_frame_accuracy": avg_frame_acc,     # 帧级准确度
            "val_action_coverage": avg_action_coverage,  # 动作集合覆盖率（原来的假象IoU）
            "val_lev_norm": avg_lev                  # 归一化编辑距离
        }
        
        self.log_dict(metrics_dict, on_step=False, on_epoch=True, prog_bar=True)
        
        # 打印详细的评估结果
        print(f"\n=== 验证结果详情 ===")
        print(f"时序段级IoU: {avg_segment_iou:.4f} (真正的时序重叠度)")
        print(f"帧级准确度: {avg_frame_acc:.4f} (逐帧预测正确率)")  
        print(f"动作覆盖率: {avg_action_coverage:.4f} (动作类型覆盖度，原假象IoU)")
        print(f"归一化编辑距离: {avg_lev:.4f} (序列相似度)")
        print("=" * 50)

        # 清空输出列表
        self.validation_step_outputs.clear()

    def test_step(self, batch, batch_idx):
        """测试步骤"""
        # 测试步骤与验证步骤相同
        return self.validation_step(batch, batch_idx)

    def forward(self, V_seq):
        """
        前向传播方法
        
        Args:
            V_seq: 输入特征序列 [seq_len, D]
            
        Returns:
            预测的动作序列 [seq_len, M]
        """
        logits_seq = []
        
        # 重置图状态
        self.g.reset()
        
        for k in range(len(V_seq)):
            V_k = V_seq[k]
            
            if self.diff_enabled and k > 0:
                # 假设使用前一帧的预测作为当前节点
                prev_node = torch.argmax(logits_seq[-1]).item()
                diff = torch.abs(V_k - self.g.proto[prev_node])
                dW_k = self.diff_mlp(diff)
                self.g.step_and_update(prev_node, dW_k)
                logits_k = self.g.W[prev_node]
            else:
                # 静态基线或第一帧：使用初始图
                # 对于第一帧，使用一个默认节点（如0）
                default_node = 0
                if self.diff_enabled:
                    diff = torch.abs(V_k - self.g.proto[default_node])
                    dW_k = self.diff_mlp(diff)
                    logits_k = self.g.get_next_step_logits(default_node, dW_k)
                else:
                    logits_k = self.g.W0[default_node]
            
            logits_seq.append(logits_k)
        
        return torch.stack(logits_seq)

    def configure_optimizers(self):
        """配置优化器"""
        if self.diff_enabled:
            # 动态方法：优化diff_mlp参数
            return torch.optim.Adam(self.diff_mlp.parameters(), lr=self.lr)
        else:
            # 静态基线：只优化可学习的偏置参数，完全不涉及差分机制
            return torch.optim.Adam([self.static_bias], lr=self.lr)
