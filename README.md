# Plan_Min: 动态任务图谱差分更新实验

[![PyTorch](https://img.shields.io/badge/PyTorch-2.4.1-red.svg)](https://pytorch.org/)
[![CUDA](https://img.shields.io/badge/CUDA-11.8-green.svg)](https://developer.nvidia.com/cuda-toolkit)
[![GPU](https://img.shields.io/badge/GPU-A6000_48GB-blue.svg)](https://www.nvidia.com/en-us/data-center/a6000/)

> 基于Breakfast数据集的动态任务图谱差分更新方法实验，对比静态基线和动态方法的性能差异。

## 🎯 项目目标

实现并对比两种任务图谱方法：
- **静态基线**: 使用固定任务图谱W₀进行动作预测
- **动态方法**: 根据特征差异动态更新任务图谱权重

## 🚀 快速使用

### 一键运行完整实验

```bash
# 激活环境
conda activate sci_1

# 运行完整对比实验（推荐）
python run_experiments.py
```

### 单独训练方法

```bash
# 训练静态基线
python train_static.py

# 训练动态方法
python train_dynamic.py
```

## 📊 训练结果

实验在NVIDIA RTX A6000 (48GB)上完成，主要输出：

```
/data2/syd_data/Breakfast_Data/Outputs/
├── static_baseline/            # 静态基线结果
│   ├── checkpoints/           # 模型权重
│   ├── final_static_model.ckpt
│   └── static_baseline_logs/  # TensorBoard日志
├── dynamic_method/            # 动态方法结果  
│   ├── checkpoints/           # 模型权重
│   ├── final_dynamic_model.ckpt
│   └── dynamic_method_logs/   # TensorBoard日志
└── experiment_report.json     # 实验配置报告
```

## 🔧 配置说明

### GPU优化配置
- **Batch Size**: 64 (充分利用A6000内存)
- **Mixed Precision**: 16-bit (提升训练速度)
- **Workers**: 8 (数据加载并行化)
- **CUDA Optimization**: cuDNN benchmark启用

### 模型参数
- **特征维度 (D)**: 64
- **动作类别 (M)**: 5 (pour_cereals, pour_milk, SIL, stir_cereals, take_bowl)
- **隐藏层维度 (H)**: 256
- **差分权重因子 (α)**: 0.1

## 📈 性能监控

```bash
# GPU监控
watch -n 1 nvidia-smi

# TensorBoard可视化
tensorboard --logdir /data2/syd_data/Breakfast_Data/Outputs

# 训练进程检查
ps aux | grep python | grep train
```

## 📁 核心文件

| 文件 | 功能 | 输出 |
|------|------|------|
| `run_experiments.py` | 完整实验运行 | 两种方法的对比结果 |
| `train_static.py` | 静态基线训练 | 基线模型和性能指标 |
| `train_dynamic.py` | 动态方法训练 | 动态模型和改进效果 |
| `configs/config_static.yaml` | 静态方法配置 | 训练超参数设置 |
| `configs/config_dynamic.yaml` | 动态方法配置 | 差分更新参数设置 |

## 🧠 技术实现

### 静态基线方法
- 使用固定的任务图邻接矩阵W₀
- 基于当前动作节点预测下一动作
- 提供性能下界参考

### 动态差分更新方法
- 计算特征与原型的差异: `diff = |V_k - proto[n_k]|`
- 通过MLP预测权重调整: `ΔW_k = MLP(diff)`
- 动态更新图权重: `W_k = W₀ + ΔW_k`

## 🎓 学术应用

本项目支持学术研究需求：
- 训练日志自动记录，支持TensorBoard可视化
- 模型权重保存，便于后续分析
- 实验配置完整记录，确保可重现性
- GPU性能优化，适合大规模实验

## 📋 系统要求

- **操作系统**: Linux (Ubuntu 18.04+)
- **GPU**: NVIDIA RTX A6000 或同等性能GPU
- **CUDA**: 11.8
- **PyTorch**: 2.4.1
- **Python**: 3.8+
- **内存**: 推荐16GB+ 系统内存

---

详细使用说明请参考 [USAGE_GUIDE.md](USAGE_GUIDE.md)
