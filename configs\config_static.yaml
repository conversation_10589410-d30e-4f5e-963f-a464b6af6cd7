# Linux_Plan_Min 静态图训练配置
# 针对A6000 48GB GPU优化的配置

# 基本配置
seed: 42
task: cereals

# 数据路径配置
data_root: /data2/syd_data/Breakfast_Data
output_dir: /data2/syd_data/Breakfast_Data/Outputs/static_baseline
stats_dir: ./stats

# 模型参数
model:
  # 特征维度 - 根据实际数据调整为64维（不包括时间戳）
  D: 64
  M: 48        # cereals任务完整的48个动作类别
  H: 256       # 增大隐藏层维度以利用GPU性能
  alpha: 0.05  # 权重调整的缩放因子（静态图不使用）
  lr: 5e-4     # 调整学习率
  diff_enabled: false  # 静态基线模式

# 训练参数 - 针对A6000优化
trainer:
  max_epochs: 50
  batch_size: 64      # 大幅增加batch size以充分利用GPU
  num_workers: 8      # 增加数据加载worker数量
  accelerator: 'gpu'  # 明确指定使用GPU
  devices: [0]        # 使用第一块GPU
  precision: '16-mixed'  # 使用推荐的混合精度训练方式
  
  # 优化配置
  gradient_clip_val: 0.5
  accumulate_grad_batches: 1
  
  # 验证配置
  val_check_interval: 0.5  # 每半个epoch验证一次
  check_val_every_n_epoch: 1
  
  # 早停配置
  enable_checkpointing: true
  enable_progress_bar: true
  enable_model_summary: true

# 数据配置
data:
  # 数据预处理参数
  normalize: true      # 启用归一化
  augmentation: false  # 时序数据不适合常规数据增强
  
  # 序列处理参数
  min_sequence_length: 5
  max_sequence_length: 2000

# 日志配置
logging:
  log_every_n_steps: 1    # 减小日志间隔以便小数据集训练
  save_top_k: 5
  monitor: val_f1
  mode: max

# 实验配置
experiment:
  name: cereals_static_baseline
  version: null
  tags: ["cereals", "static_baseline", "breakfast", "gpu_optimized"]

# 调试配置
debug:
  fast_dev_run: false
  overfit_batches: 0
  limit_train_batches: 1.0
  limit_val_batches: 1.0

# Hydra配置
hydra:
  run:
    dir: ${output_dir}/hydra_runs/${now:%Y-%m-%d_%H-%M-%S}
  job:
    chdir: true 