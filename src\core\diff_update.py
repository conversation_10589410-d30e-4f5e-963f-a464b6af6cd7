import torch
import torch.nn as nn
from typing import Union


class DiffMLP(nn.Module):
    """
    差分更新MLP网络
    
    根据当前帧特征与对应动作原型的差异，预测任务图边权重的调整量
    输出范围被限制在(-alpha, alpha)之间
    """
    
    def __init__(self, D: int, M: int, H: int = 256, alpha: float = 0.05):
        """
        Args:
            D: 输入特征维度
            M: 动作类别数量（输出维度）
            H: 隐藏层维度
            alpha: 输出缩放因子，限制调整量的范围
        """
        super().__init__()
        self.alpha = alpha
        
        # 两层全连接网络
        self.mlp = nn.Sequential(
            nn.Linear(D, H),
            nn.ReLU(),
            nn.Linear(H, M),
            nn.Tanh()  # 输出范围 (-1, 1)
        )
    
    def forward(self, diff: torch.Tensor) -> torch.Tensor:
        """
        前向传播

        Args:
            diff: 差分特征，形状为 (D,) 或 (batch_size, D)

        Returns:
            调整量，形状为 (M,) 或 (batch_size, M)，范围在 (-alpha, alpha)

        Raises:
            ValueError: 如果输入张量维度不正确
        """
        if diff.dim() not in [1, 2]:
            raise ValueError(f"Expected 1D or 2D tensor, got {diff.dim()}D tensor")

        if diff.shape[-1] != self.mlp[0].in_features:
            raise ValueError(f"Expected last dimension to be {self.mlp[0].in_features}, got {diff.shape[-1]}")

        return self.alpha * self.mlp(diff)
