# Plan_Min 使用指南

## 📋 项目概述

Plan_Min是一个动态任务图谱差分更新实验项目，用于Breakfast数据集的动作识别任务。项目实现了静态基线方法和动态差分更新方法的对比实验。

## 🗂️ 项目结构

```
Plan_Min/
├── configs/                    # 配置文件
│   ├── config_static.yaml     # 静态基线配置
│   └── config_dynamic.yaml    # 动态方法配置
├── src/                        # 源代码
│   ├── core/                   # 核心模块
│   ├── data/                   # 数据处理
│   ├── pipeline/               # 训练流程
│   └── utils/                  # 工具函数
├── train_static.py             # 静态基线训练脚本
├── train_dynamic.py            # 动态方法训练脚本
├── run_experiments.py          # 完整实验运行脚本
└── requirements.txt            # 依赖包列表
```

## 🚀 快速开始

### 1. 环境配置

```bash
# 激活虚拟环境
conda activate sci_1

# 安装依赖
pip install -r requirements.txt
```

### 2. 完整实验运行

```bash
# 运行完整实验（推荐）
python run_experiments.py
```

**执行效果:**
- 自动按顺序训练静态基线和动态方法
- GPU优化配置，充分利用A6000性能
- 实时显示训练进度和GPU使用情况
- 自动保存模型检查点和训练日志

**输出数据保存位置:**
```
/data2/syd_data/Breakfast_Data/Outputs/
├── static_baseline/            # 静态基线结果
│   ├── checkpoints/           # 模型检查点
│   ├── static_baseline_logs/  # TensorBoard日志
│   └── final_static_model.ckpt
├── dynamic_method/            # 动态方法结果
│   ├── checkpoints/           # 模型检查点
│   ├── dynamic_method_logs/   # TensorBoard日志
│   └── final_dynamic_model.ckpt
└── experiment_report.json     # 实验总结报告
```

## 🔧 单独训练方法

### 1. 静态基线训练

```bash
python train_static.py
```

**执行效果:**
- 训练不使用差分更新的静态任务图模型
- 50个epoch，batch_size=64，混合精度训练
- 早停机制，防止过拟合
- 自动保存最优模型

**配置参数 (configs/config_static.yaml):**
- `diff_enabled: false` - 关闭差分更新
- `batch_size: 64` - GPU优化批次大小
- `precision: 16` - 混合精度训练
- `max_epochs: 50` - 最大训练轮数

### 2. 动态方法训练

```bash
python train_dynamic.py
```

**执行效果:**
- 训练带差分更新的动态任务图模型
- 如果存在静态基线，会自动加载权重初始化
- 相同的GPU优化设置
- 输出与静态方法的性能对比

**配置参数 (configs/config_dynamic.yaml):**
- `diff_enabled: true` - 启用差分更新
- `alpha: 0.1` - 差分更新权重因子
- 其他参数与静态配置相同

## 📊 监控和分析

### 1. 实时GPU监控

```bash
# 监控GPU使用情况
watch -n 1 nvidia-smi
```

**显示信息:**
- GPU利用率（目标: >80%）
- 显存使用（A6000: 47.5GB可用）
- 功耗和温度状态

### 2. TensorBoard可视化

```bash
# 启动TensorBoard
tensorboard --logdir /data2/syd_data/Breakfast_Data/Outputs
```

**可视化内容:**
- 训练/验证损失曲线
- 评估指标（IoU, F1-score等）
- 模型参数分布
- 学习率变化

### 3. 训练进程检查

```bash
# 查看训练进程
ps aux | grep python | grep -E "(train_static|train_dynamic)"

# 查看训练日志
tail -f /data2/syd_data/Breakfast_Data/Outputs/*/lightning_logs/version_*/metrics.csv
```

## 📈 结果分析

### 1. 模型性能指标

训练完成后，主要评估指标包括:
- **IoU (Intersection over Union)**: 动作分割重叠度
- **F1-Score**: 分类准确性
- **Edit Distance**: 序列编辑距离
- **Training Time**: 训练耗时

### 2. 对比分析

**预期结果:**
- 静态基线: 提供性能下界，训练稳定
- 动态方法: 在复杂序列上性能提升，但训练时间稍长

### 3. 学术输出文件

```
/data2/syd_data/Breakfast_Data/Outputs/
├── experiment_report.json     # 实验配置和基本结果
├── static_baseline/
│   └── metrics.json          # 静态方法详细指标
└── dynamic_method/
    └── metrics.json          # 动态方法详细指标
```

## 🐛 常见问题

### 1. GPU内存不足

```bash
# 减小批次大小
# 编辑配置文件中的 batch_size: 32 或更小
```

### 2. 训练中断恢复

```bash
# 训练会自动保存检查点，可以从最后一个检查点恢复
# 检查点保存在: /data2/syd_data/Breakfast_Data/Outputs/*/checkpoints/
```

### 3. 数据加载错误

```bash
# 检查数据路径是否正确
ls /data2/syd_data/Breakfast_Data/breakfast_data/
ls /data2/syd_data/Breakfast_Data/segmentation_coarse/
```

## ⚡ 性能优化建议

### 1. GPU设置
- 使用单块A6000 GPU，避免多GPU通信开销
- 启用混合精度训练（16位）
- 设置合适的num_workers（推荐8）

### 2. 训练策略
- 先训练静态基线，了解数据集特性
- 动态方法基于静态基线初始化
- 使用早停机制避免过拟合

### 3. 批次大小调优
- A6000 48GB: 推荐batch_size=64
- 如果内存不足，降至32或16
- 序列越长，批次应越小

## 📝 实验记录

建议每次实验后记录:
1. 训练配置（batch_size, lr, epochs等）
2. 最终性能指标
3. 训练时间和GPU使用率
4. 遇到的问题和解决方案

这样有助于优化后续实验和撰写学术论文。 